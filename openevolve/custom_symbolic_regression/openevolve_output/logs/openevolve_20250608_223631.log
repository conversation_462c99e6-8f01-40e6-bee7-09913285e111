2025-06-08 22:36:31,674 - openevolve.controller - INFO - Logging to openevolve_output/logs/openevolve_20250608_223631.log
2025-06-08 22:36:31,722 - openevolve.llm.openai - INFO - Initialized OpenAI LLM with model: gpt-4o
2025-06-08 22:36:31,757 - openevolve.llm.openai - INFO - Initialized OpenAI LLM with model: gpt-4o-mini
2025-06-08 22:36:31,758 - openevolve.llm.ensemble - INFO - Initialized LLM ensemble with models: gpt-4o (weight: 0.70), gpt-4o-mini (weight: 0.30)
2025-06-08 22:36:31,785 - openevolve.llm.openai - INFO - Initialized OpenAI LLM with model: gpt-4o
2025-06-08 22:36:31,811 - openevolve.llm.openai - INFO - Initialized OpenAI LLM with model: gpt-4o-mini
2025-06-08 22:36:31,812 - openevolve.llm.ensemble - INFO - Initialized LLM ensemble with models: gpt-4o (weight: 0.70), gpt-4o-mini (weight: 0.30)
2025-06-08 22:36:31,812 - openevolve.prompt.sampler - INFO - Initialized prompt sampler
2025-06-08 22:36:31,812 - openevolve.prompt.sampler - INFO - Initialized prompt sampler
2025-06-08 22:36:31,812 - openevolve.prompt.sampler - INFO - Set custom templates: system=evaluator_system_message, user=None
2025-06-08 22:36:31,812 - openevolve.database - INFO - Initialized program database with 0 programs
2025-06-08 22:36:32,007 - openevolve.evaluator - INFO - Successfully loaded evaluation function from evaluator.py
2025-06-08 22:36:32,007 - openevolve.evaluator - INFO - Initialized evaluator with evaluator.py
2025-06-08 22:36:32,007 - openevolve.controller - INFO - Initialized OpenEvolve with initial_program.py and evaluator.py
