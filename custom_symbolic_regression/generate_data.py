"""
Generate synthetic data for the equation y = exp(2*x) + x^2 - 2*x
This script creates training and test datasets for symbolic regression.
"""

import numpy as np
import matplotlib.pyplot as plt
import os

def target_function(x):
    """
    The target function: y = exp(2*x) + x^2 - 2*x
    
    Args:
        x: Input values
        
    Returns:
        y: Output values according to the target equation
    """
    return np.exp(2 * x) + x**2 - 2*x

def generate_dataset(x_min=-1.5, x_max=1.5, n_train=100, n_test=50, noise_std=0.1, seed=42):
    """
    Generate training and test datasets for the target function.
    
    Args:
        x_min: Minimum x value
        x_max: Maximum x value
        n_train: Number of training samples
        n_test: Number of test samples
        noise_std: Standard deviation of Gaussian noise to add
        seed: Random seed for reproducibility
        
    Returns:
        Dictionary containing train and test data
    """
    np.random.seed(seed)
    
    # Generate training data
    x_train = np.random.uniform(x_min, x_max, n_train)
    y_train_clean = target_function(x_train)
    y_train = y_train_clean + np.random.normal(0, noise_std, n_train)
    
    # Generate test data (different distribution for better evaluation)
    x_test = np.linspace(x_min, x_max, n_test)
    y_test_clean = target_function(x_test)
    y_test = y_test_clean + np.random.normal(0, noise_std, n_test)
    
    # Generate out-of-distribution test data
    x_ood_test = np.random.uniform(x_max, x_max + 0.5, n_test // 2)
    y_ood_test_clean = target_function(x_ood_test)
    y_ood_test = y_ood_test_clean + np.random.normal(0, noise_std, n_test // 2)
    
    return {
        'train': {
            'x': x_train,
            'y': y_train,
            'y_clean': y_train_clean
        },
        'test': {
            'x': x_test,
            'y': y_test,
            'y_clean': y_test_clean
        },
        'ood_test': {
            'x': x_ood_test,
            'y': y_ood_test,
            'y_clean': y_ood_test_clean
        }
    }

def save_data_for_openevolve(data, output_dir="custom_problem"):
    """
    Save data in the format expected by OpenEvolve.
    
    Args:
        data: Dictionary containing train/test data
        output_dir: Directory to save the data
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # Prepare data in the format expected by OpenEvolve
    # X should be (n_samples, n_features), y should be (n_samples,)
    
    # Training data
    X_train = data['train']['x'].reshape(-1, 1)  # Single feature: x
    y_train = data['train']['y']
    
    # Test data
    X_test = data['test']['x'].reshape(-1, 1)
    y_test = data['test']['y']
    
    # OOD test data
    X_ood_test = data['ood_test']['x'].reshape(-1, 1)
    y_ood_test = data['ood_test']['y']
    
    # Save as numpy arrays
    np.save(os.path.join(output_dir, "X_train_for_eval.npy"), X_train)
    np.save(os.path.join(output_dir, "y_train_for_eval.npy"), y_train)
    np.save(os.path.join(output_dir, "X_test_for_eval.npy"), X_test)
    np.save(os.path.join(output_dir, "y_test_for_eval.npy"), y_test)
    np.save(os.path.join(output_dir, "X_ood_test_for_eval.npy"), X_ood_test)
    np.save(os.path.join(output_dir, "y_ood_test_for_eval.npy"), y_ood_test)
    
    print(f"Data saved to {output_dir}/")
    print(f"Training samples: {len(X_train)}")
    print(f"Test samples: {len(X_test)}")
    print(f"OOD test samples: {len(X_ood_test)}")
    
    return output_dir

def plot_data(data, save_path=None):
    """
    Plot the generated data to visualize the target function.
    
    Args:
        data: Dictionary containing train/test data
        save_path: Optional path to save the plot
    """
    plt.figure(figsize=(12, 8))
    
    # Plot training data
    plt.subplot(2, 2, 1)
    plt.scatter(data['train']['x'], data['train']['y'], alpha=0.6, label='Training data (noisy)')
    plt.plot(np.sort(data['train']['x']), target_function(np.sort(data['train']['x'])), 'r-', label='True function')
    plt.xlabel('x')
    plt.ylabel('y')
    plt.title('Training Data')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot test data
    plt.subplot(2, 2, 2)
    plt.scatter(data['test']['x'], data['test']['y'], alpha=0.6, label='Test data (noisy)', color='orange')
    plt.plot(data['test']['x'], data['test']['y_clean'], 'r-', label='True function')
    plt.xlabel('x')
    plt.ylabel('y')
    plt.title('Test Data')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot OOD test data
    plt.subplot(2, 2, 3)
    plt.scatter(data['ood_test']['x'], data['ood_test']['y'], alpha=0.6, label='OOD test data (noisy)', color='green')
    plt.plot(data['ood_test']['x'], data['ood_test']['y_clean'], 'r-', label='True function')
    plt.xlabel('x')
    plt.ylabel('y')
    plt.title('Out-of-Distribution Test Data')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot all data together
    plt.subplot(2, 2, 4)
    x_full = np.linspace(-2, 2.5, 200)
    y_full = target_function(x_full)
    plt.plot(x_full, y_full, 'r-', linewidth=2, label='y = exp(2x) + x² - 2x')
    plt.scatter(data['train']['x'], data['train']['y'], alpha=0.4, label='Training', s=20)
    plt.scatter(data['test']['x'], data['test']['y'], alpha=0.4, label='Test', s=20)
    plt.scatter(data['ood_test']['x'], data['ood_test']['y'], alpha=0.4, label='OOD Test', s=20)
    plt.xlabel('x')
    plt.ylabel('y')
    plt.title('Complete Dataset Overview')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Plot saved to {save_path}")
    
    plt.show()

if __name__ == "__main__":
    # Generate the dataset
    print("Generating dataset for y = exp(2*x) + x^2 - 2*x")
    data = generate_dataset()
    
    # Save data for OpenEvolve
    output_dir = save_data_for_openevolve(data)
    
    # Create visualization
    plot_data(data, save_path=os.path.join(output_dir, "data_visualization.png"))
    
    print("\nDataset generation complete!")
    print("Target equation: y = exp(2*x) + x^2 - 2*x")
    print(f"Data range: x ∈ [-1.5, 1.5] for training/test")
    print(f"OOD range: x ∈ [1.5, 2.0] for out-of-distribution testing")
