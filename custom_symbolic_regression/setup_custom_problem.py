"""
Setup script for custom symbolic regression problem: y = exp(2*x) + x^2 - 2*x
This script generates the data and sets up the environment for OpenEvolve.
"""

import os
import sys
import subprocess
from generate_data import generate_dataset, save_data_for_openevolve, plot_data

def check_api_keys():
    """Check if API keys are configured."""
    print("Checking API key configuration...")
    
    # Check environment variables
    openai_key = os.environ.get("OPENAI_API_KEY")
    gemini_key = os.environ.get("GEMINI_API_KEY")
    
    if openai_key:
        print("✓ OPENAI_API_KEY found in environment variables")
    else:
        print("✗ OPENAI_API_KEY not found in environment variables")
    
    if gemini_key:
        print("✓ GEMINI_API_KEY found in environment variables")
    else:
        print("✗ GEMINI_API_KEY not found in environment variables")
    
    # Check secrets.yaml file
    secrets_file = "secrets.yaml"
    if os.path.exists(secrets_file):
        print(f"✓ {secrets_file} file found")
    else:
        print(f"✗ {secrets_file} file not found")
        print(f"  You can copy secrets.yaml.template to secrets.yaml and fill in your API keys")
    
    if not (openai_key or gemini_key or os.path.exists(secrets_file)):
        print("\n⚠️  WARNING: No API keys configured!")
        print("   Please set up at least one of the following:")
        print("   1. Set OPENAI_API_KEY environment variable")
        print("   2. Set GEMINI_API_KEY environment variable") 
        print("   3. Create secrets.yaml file with your API keys")
        print("\n   Example for environment variables:")
        print("   export OPENAI_API_KEY='your-openai-api-key-here'")
        print("   export GEMINI_API_KEY='your-gemini-api-key-here'")
        return False
    
    return True

def setup_data():
    """Generate and setup the dataset."""
    print("\n" + "="*60)
    print("SETTING UP CUSTOM SYMBOLIC REGRESSION PROBLEM")
    print("Target equation: y = exp(2*x) + x^2 - 2*x")
    print("="*60)
    
    # Generate the dataset
    print("\n1. Generating synthetic dataset...")
    data = generate_dataset(
        x_min=-1.5, 
        x_max=1.5, 
        n_train=150, 
        n_test=50, 
        noise_std=0.05,  # Lower noise for better learning
        seed=42
    )
    
    # Save data for OpenEvolve
    print("\n2. Saving data in OpenEvolve format...")
    output_dir = save_data_for_openevolve(data, "custom_problem")
    
    # Create visualization
    print("\n3. Creating data visualization...")
    try:
        plot_data(data, save_path=os.path.join(output_dir, "data_visualization.png"))
    except Exception as e:
        print(f"Warning: Could not create plot: {e}")
        print("This is likely due to missing display or matplotlib backend issues.")
    
    return output_dir

def create_run_script():
    """Create a script to run the symbolic regression."""
    run_script_content = '''#!/bin/bash

# Run script for custom symbolic regression problem
# Usage: ./run_symbolic_regression.sh

echo "Starting OpenEvolve for custom symbolic regression..."
echo "Target equation: y = exp(2*x) + x^2 - 2*x"
echo ""

# Activate virtual environment if it exists
if [ -d "openevolve_env" ]; then
    echo "Activating virtual environment..."
    source openevolve_env/bin/activate
fi

# Check if data exists
if [ ! -f "custom_problem/X_train_for_eval.npy" ]; then
    echo "Data not found. Generating dataset..."
    python generate_data.py
fi

# Run OpenEvolve
echo "Running OpenEvolve..."
python -c "
from openevolve import OpenEvolve

# Initialize OpenEvolve
evolve = OpenEvolve(
    initial_program_path='initial_program.py',
    evaluation_file='evaluator.py',
    config_path='config.yaml'
)

# Run the evolution
print('Starting evolution process...')
evolve.run()
print('Evolution completed!')
"

echo ""
echo "Evolution completed! Check the results in the generated files."
'''
    
    with open("run_symbolic_regression.sh", "w") as f:
        f.write(run_script_content)
    
    # Make it executable
    os.chmod("run_symbolic_regression.sh", 0o755)
    print("✓ Created run_symbolic_regression.sh script")

def print_instructions():
    """Print setup completion instructions."""
    print("\n" + "="*60)
    print("SETUP COMPLETE!")
    print("="*60)
    
    print("\nFiles created:")
    print("  ✓ custom_problem/          - Dataset directory")
    print("  ✓ initial_program.py       - Starting linear model")
    print("  ✓ evaluator.py            - Model evaluation script")
    print("  ✓ config.yaml             - OpenEvolve configuration")
    print("  ✓ secrets.yaml.template   - API keys template")
    print("  ✓ run_symbolic_regression.sh - Run script")
    
    print("\nNext steps:")
    print("1. Configure your API keys:")
    print("   Option A: Set environment variables:")
    print("     export OPENAI_API_KEY='your-openai-api-key-here'")
    print("     export GEMINI_API_KEY='your-gemini-api-key-here'")
    print("   Option B: Copy secrets.yaml.template to secrets.yaml and fill in keys")
    
    print("\n2. Choose your LLM configuration in config.yaml:")
    print("   - OpenAI models (default): gpt-4o, gpt-4o-mini")
    print("   - Gemini models: gemini-2.0-flash-lite, gemini-2.0-flash")
    print("   - Mixed models: combination of both")
    
    print("\n3. Run the symbolic regression:")
    print("   ./run_symbolic_regression.sh")
    print("   OR")
    print("   python -c \"from openevolve import OpenEvolve; OpenEvolve('initial_program.py', 'evaluator.py', 'config.yaml').run()\"")
    
    print("\nTarget equation: y = exp(2*x) + x^2 - 2*x")
    print("The goal is for OpenEvolve to discover this equation structure!")

def main():
    """Main setup function."""
    # Check API keys
    api_keys_ok = check_api_keys()
    
    # Setup data regardless of API keys (user can configure them later)
    output_dir = setup_data()
    
    # Create run script
    create_run_script()
    
    # Print instructions
    print_instructions()
    
    if not api_keys_ok:
        print("\n⚠️  Remember to configure your API keys before running!")

if __name__ == "__main__":
    main()
