# Configuration for Custom Symbolic Regression Task: y = exp(2*x) + x^2 - 2*x
max_iterations: 100
log_level: "INFO"
target_score: "combined_score"
checkpoint_interval: 10

# LLM configuration - supports both OpenAI and Gemini APIs
llm:
  # You can choose between different model configurations:
  # Option 1: OpenAI models (requires OPENAI_API_KEY)
  primary_model: "gpt-4o"
  primary_model_weight: 0.7
  secondary_model: "gpt-4o-mini"
  secondary_model_weight: 0.3
  api_base: "https://api.openai.com/v1"
  
  # Option 2: Gemini models (requires GEMINI_API_KEY, uncomment to use)
  # primary_model: "gemini-2.0-flash-lite"
  # primary_model_weight: 0.7
  # secondary_model: "gemini-2.0-flash"
  # secondary_model_weight: 0.3
  # api_base: "https://generativelanguage.googleapis.com/v1beta/openai/"
  
  # Option 3: Mixed models (requires both API keys, uncomment to use)
  # primary_model: "gpt-4o"
  # primary_model_weight: 0.5
  # secondary_model: "gemini-2.0-flash-lite"
  # secondary_model_weight: 0.5
  # Note: For mixed models, you'll need to modify the config to support multiple API bases
  
  temperature: 0.7
  top_p: 0.95
  max_tokens: 4096
  timeout: 60
  retries: 3
  retry_delay: 5

# Prompt configuration
prompt:
  system_message: |
    Your task is to evolve a Python function `func(x, params)` that models a mathematical relationship
    by predicting output variables based on input variables.

    The function signature is:

    ```python
    def func(x: np.ndarray, params: np.ndarray) -> np.ndarray:
    ```

    - `x` is a 2D NumPy array of shape `(n_samples, 1)` containing the input variable
    - `params` is a 1D NumPy array of up to 10 parameters
    - The function should return a 1D NumPy array of predictions with shape `(n_samples,)`

    **Current Problem:**
    Model the relationship y = f(x) where the true underlying function is y = exp(2*x) + x^2 - 2*x
    Thus, `x` contains 1 column: the input variable x.

    The initial version of `func` is a simple linear model. Parameters in `params` will be optimized externally
    using the BFGS algorithm based on training data.

    Your objective is to evolve `func` to improve predictive performance. Aim for a balance between:
    - **Accuracy**: Lower mean squared error (MSE) on training data
    - **Simplicity**: Prefer concise, interpretable expressions
    - **Mathematical insight**: Try to discover the true underlying mathematical structure

    Model performance (score = -log_10(mse)) will be evaluated on a held-out dataset.
    Ensure the model is free of potential numerical errors (e.g., log(0), division by 0, overflow).
    
    Consider mathematical functions like:
    - Polynomial terms: x, x^2, x^3, etc.
    - Exponential functions: exp(x), exp(2*x), etc.
    - Trigonometric functions: sin(x), cos(x), etc.
    - Logarithmic functions: log(x), log(1+x), etc.
    - Combinations of the above
    
    The target equation contains exponential and polynomial terms, so focus on those patterns.

  num_top_programs: 4
  use_template_stochasticity: true

# Database configuration
database:
  population_size: 50
  archive_size: 20
  num_islands: 3
  elite_selection_ratio: 0.3
  exploitation_ratio: 0.6

# Evaluator configuration
evaluator:
  timeout: 90
  cascade_evaluation: false
  cascade_thresholds: [1.0]
  parallel_evaluations: 3
  use_llm_feedback: false

# Evolution settings
diff_based_evolution: true
allow_full_rewrites: false
